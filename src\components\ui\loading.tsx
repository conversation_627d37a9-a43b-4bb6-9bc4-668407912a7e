import React from "react";
import { Icon } from "@iconify/react";
import { cn } from "@/lib/utils";

interface LoadingSpinnerProps {
  size?: "sm" | "md" | "lg";
  className?: string;
  message?: string;
  centered?: boolean;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = "md",
  className,
  message,
  centered = false,
}) => {
  const sizeClasses = {
    sm: "h-4 w-4",
    md: "h-6 w-6",
    lg: "h-8 w-8",
  };

  const spinner = (
    <Icon
      icon="lucide:loader-2"
      className={cn("animate-spin text-teal-600", sizeClasses[size], className)}
      role="status"
      aria-label="Loading"
    />
  );

  if (message || centered) {
    return (
      <div
        className={cn(
          "flex items-center justify-center",
          centered && "min-h-[200px]",
          className
        )}
      >
        <div className="flex items-center space-x-3">
          {spinner}
          {message && (
            <span className="text-gray-500" aria-live="polite">
              {message}
            </span>
          )}
        </div>
      </div>
    );
  }

  return spinner;
};

interface LoadingButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  isLoading: boolean;
  children: React.ReactNode;
  loadingText?: string;
}

export const LoadingButton: React.FC<LoadingButtonProps> = ({
  isLoading,
  children,
  loadingText,
  className,
  disabled,
  ...props
}) => {
  return (
    <button
      className={cn(
        "inline-flex items-center justify-center",
        "disabled:opacity-50 disabled:cursor-not-allowed",
        className
      )}
      disabled={isLoading || disabled}
      {...props}
    >
      {isLoading && (
        <Icon
          icon="lucide:loader-2"
          className="animate-spin h-4 w-4 mr-2 text-current"
        />
      )}
      {isLoading ? loadingText || "Loading..." : children}
    </button>
  );
};

interface LoadingOverlayProps {
  isLoading: boolean;
  message?: string;
  className?: string;
}

export const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  isLoading,
  message = "Loading...",
  className,
}) => {
  if (!isLoading) return null;

  return (
    <div
      className={cn(
        "absolute inset-0 bg-white/80 backdrop-blur-sm",
        "flex items-center justify-center",
        "z-50",
        className
      )}
    >
      <div className="flex flex-col items-center space-y-2">
        <LoadingSpinner size="lg" />
        <p className="text-sm text-gray-600">{message}</p>
      </div>
    </div>
  );
};

interface LoadingSkeletonProps {
  className?: string;
  lines?: number;
}

export const LoadingSkeleton: React.FC<LoadingSkeletonProps> = ({
  className,
  lines = 1,
}) => {
  return (
    <div className={cn("animate-pulse", className)}>
      {Array.from({ length: lines }).map((_, index) => (
        <div
          key={index}
          className={cn(
            "bg-gray-200 rounded",
            index === 0 ? "h-4" : "h-3 mt-2",
            index === lines - 1 ? "w-3/4" : "w-full"
          )}
        />
      ))}
    </div>
  );
};

interface LoadingCardProps {
  className?: string;
}

export const LoadingCard: React.FC<LoadingCardProps> = ({ className }) => {
  return (
    <div className={cn("p-4 border rounded-lg", className)}>
      <div className="animate-pulse">
        <div className="flex items-center space-x-4">
          <div className="rounded-full bg-gray-200 h-12 w-12"></div>
          <div className="flex-1 space-y-2">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-3 bg-gray-200 rounded w-1/2"></div>
          </div>
        </div>
        <div className="mt-4 space-y-2">
          <div className="h-3 bg-gray-200 rounded"></div>
          <div className="h-3 bg-gray-200 rounded w-5/6"></div>
        </div>
      </div>
    </div>
  );
};

interface LoadingDotsProps {
  className?: string;
}

export const LoadingDots: React.FC<LoadingDotsProps> = ({ className }) => {
  return (
    <div className={cn("flex space-x-1", className)}>
      {[0, 1, 2].map((index) => (
        <div
          key={index}
          className="w-2 h-2 bg-current rounded-full animate-pulse"
          style={{
            animationDelay: `${index * 0.2}s`,
            animationDuration: "1s",
          }}
        />
      ))}
    </div>
  );
};

// Progress bar component
interface ProgressBarProps {
  progress: number; // 0-100
  className?: string;
  showPercentage?: boolean;
}

export const ProgressBar: React.FC<ProgressBarProps> = ({
  progress,
  className,
  showPercentage = false,
}) => {
  return (
    <div className={cn("w-full", className)}>
      <div className="flex justify-between items-center mb-1">
        {showPercentage && (
          <span className="text-sm text-gray-600">{Math.round(progress)}%</span>
        )}
      </div>
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div
          className="bg-teal-600 h-2 rounded-full transition-all duration-300 ease-out"
          style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
        />
      </div>
    </div>
  );
};

// Pulse animation for loading states
interface PulseProps {
  children: React.ReactNode;
  isLoading: boolean;
  className?: string;
}

export const Pulse: React.FC<PulseProps> = ({
  children,
  isLoading,
  className,
}) => {
  return (
    <div className={cn(isLoading && "animate-pulse opacity-50", className)}>
      {children}
    </div>
  );
};

// Product Card Loading Skeleton
interface ProductCardSkeletonProps {
  className?: string;
}

export const ProductCardSkeleton: React.FC<ProductCardSkeletonProps> = ({
  className,
}) => {
  return (
    <div className={cn("bg-white rounded-lg shadow-sm border p-4", className)}>
      <div className="animate-pulse">
        {/* Image skeleton */}
        <div className="aspect-square bg-gray-200 rounded-lg mb-4"></div>

        {/* Title skeleton */}
        <div className="h-4 bg-gray-200 rounded mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-3/4 mb-3"></div>

        {/* Price skeleton */}
        <div className="h-5 bg-gray-200 rounded w-1/2 mb-2"></div>

        {/* Location skeleton */}
        <div className="h-3 bg-gray-200 rounded w-2/3"></div>
      </div>
    </div>
  );
};

// Page Loading Component
interface PageLoadingProps {
  message?: string;
  className?: string;
}

export const PageLoading: React.FC<PageLoadingProps> = ({
  message = "Loading page...",
  className,
}) => {
  return (
    <div
      className={cn(
        "min-h-screen bg-[#EFEFEF] flex items-center justify-center",
        className
      )}
    >
      <div className="text-center">
        <LoadingSpinner size="lg" />
        <p className="mt-4 text-gray-600">{message}</p>
      </div>
    </div>
  );
};

// Search Loading Component
interface SearchLoadingProps {
  className?: string;
}

export const SearchLoading: React.FC<SearchLoadingProps> = ({ className }) => {
  return (
    <div className={cn("flex items-center justify-center py-8", className)}>
      <div className="text-center">
        <Icon
          icon="lucide:search"
          className="h-8 w-8 text-gray-400 mx-auto mb-2 animate-pulse"
        />
        <p className="text-gray-500">Searching...</p>
      </div>
    </div>
  );
};

const LoadingComponents = {
  LoadingSpinner,
  LoadingButton,
  LoadingOverlay,
  LoadingSkeleton,
  LoadingCard,
  LoadingDots,
  ProgressBar,
  Pulse,
  ProductCardSkeleton,
  PageLoading,
  SearchLoading,
};

export default LoadingComponents;
